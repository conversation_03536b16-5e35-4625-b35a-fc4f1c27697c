# 样式修复指南 - 行高问题

## 问题描述

在从设计稿（蓝湖/Figma）自动生成的代码中，发现编译后的页面存在样式问题：
- 部分组件向下偏移
- 文本显示异常，占用过多垂直空间
- 页面布局不符合设计稿预期

## 问题根因

经过分析发现，问题出现在样式文件中设置了过大的 `line-height` 值：
- 大量文本元素设置了 `line-height: 200rpx`（相当于约100px）
- 部分元素设置了 `line-height: 300rpx`（相当于约150px）
- 这些过大的行高导致文本元素占用了过多的垂直空间，使得页面组件向下偏移

## 影响范围

这个问题可能存在于所有从设计稿自动生成的页面中，特别是：
- `src/pages/lanhu_*` 开头的页面
- 包含大量文本元素的页面
- 使用 `.rpx.scss` 样式文件的页面

## 修复方案

### 1. 识别问题样式

在样式文件中搜索以下模式：
```scss
line-height: 200rpx;
line-height: 300rpx;
```

### 2. 修复原则

将过大的行高值修改为与字体大小相匹配的合理值：

| 字体大小 | 建议行高 | 说明 |
|---------|---------|------|
| 16rpx | 16rpx - 20rpx | 小字体，行高可以等于或略大于字体大小 |
| 20rpx | 20rpx - 24rpx | 中小字体 |
| 24rpx | 24rpx - 28rpx | 常规字体 |
| 26rpx | 26rpx - 30rpx | 中等字体 |
| 30rpx | 30rpx - 36rpx | 中大字体 |
| 32rpx | 32rpx - 38rpx | 大字体 |
| 36rpx | 36rpx - 42rpx | 较大字体 |
| 44rpx | 44rpx - 50rpx | 标题字体 |
| 66rpx | 66rpx - 72rpx | 大标题字体 |

### 3. 具体修复步骤

#### 步骤1：备份原文件
```bash
# 建议先备份原样式文件
cp src/pages/页面名/assets/style/index.rpx.scss src/pages/页面名/assets/style/index.rpx.scss.backup
```

#### 步骤2：批量查找替换
使用编辑器的查找替换功能，或者手动修改：

**常见的修复映射：**
```scss
/* 修复前 → 修复后 */
line-height: 300rpx; → line-height: 36rpx; /* 对应24rpx字体 */
line-height: 200rpx; → 根据对应字体大小调整：
  - font-size: 16rpx → line-height: 16rpx;
  - font-size: 20rpx → line-height: 20rpx;
  - font-size: 24rpx → line-height: 24rpx;
  - font-size: 26rpx → line-height: 26rpx;
  - font-size: 30rpx → line-height: 30rpx;
  - font-size: 32rpx → line-height: 32rpx;
  - font-size: 36rpx → line-height: 36rpx;
```

#### 步骤3：验证修复
1. 检查样式文件语法是否正确
2. 重新编译项目
3. 对比修复前后的页面效果
4. 确认组件位置恢复正常

## 示例修复

### 修复前：
```scss
.text_1 {
  width: 64rpx;
  height: 36rpx;
  font-size: 24rpx;
  line-height: 300rpx; // 问题：行高过大
  margin: 14rpx 0 0 32rpx;
}

.text_2 {
  width: 128rpx;
  height: 32rpx;
  font-size: 32rpx;
  line-height: 200rpx; // 问题：行高过大
  margin: 4rpx 0 0 56rpx;
}
```

### 修复后：
```scss
.text_1 {
  width: 64rpx;
  height: 36rpx;
  font-size: 24rpx;
  line-height: 24rpx; // 修复：行高与字体大小匹配
  margin: 14rpx 0 0 32rpx;
}

.text_2 {
  width: 128rpx;
  height: 32rpx;
  font-size: 32rpx;
  line-height: 32rpx; // 修复：行高与字体大小匹配
  margin: 4rpx 0 0 56rpx;
}
```

## 预防措施

1. **代码生成后检查**：每次从设计稿生成代码后，都应该检查样式文件中的行高设置
2. **建立样式规范**：制定团队的行高设置规范，避免使用过大的行高值
3. **自动化检测**：可以编写脚本自动检测和修复过大的行高值

## 相关页面

**已检测到存在此问题的页面（共计约60个页面）：**
- `src/pages/lanhu_shouyexiangmuliebiao/` - 首页项目列表页
- `src/pages/lanhu_wodeshouyi/` - 我的收益页面
- `src/pages/lanhu_wuliaoshangcheng/` - 物料商城页面
- `src/pages/lanhu_yiwancheng/` - 已完成页面
- `src/pages/lanhu_zhanghaoxinxi/` - 账号信息页面
- 以及其他所有 `lanhu_*` 开头的页面

**问题统计：**
- 总计发现约1000+个行高问题
- 主要问题值：`line-height: 200rpx`、`line-height: 300rpx`
- 特殊问题值：`line-height: 228rpx`、`line-height: 298rpx`

**使用自动化工具检测：**
```bash
# 检测所有问题页面
node scripts/detect-line-height-issues.js

# 修复指定页面（试运行）
node scripts/fix-line-height.js src/pages/页面名/assets/style/index.rpx.scss

# 实际修复
node scripts/fix-line-height.js --fix src/pages/页面名/assets/style/index.rpx.scss
```

## 快速修复脚本

可以使用以下Node.js脚本来批量修复行高问题：

```javascript
// fix-line-height.js
const fs = require('fs');
const path = require('path');

function fixLineHeight(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // 修复映射表
    const fixes = [
      { from: /line-height:\s*300rpx;/g, to: 'line-height: 36rpx;' },
      { from: /line-height:\s*200rpx;/g, to: 'line-height: 24rpx;' } // 默认值，可能需要手动调整
    ];

    let hasChanges = false;
    fixes.forEach(fix => {
      if (fix.from.test(content)) {
        content = content.replace(fix.from, fix.to);
        hasChanges = true;
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
    } else {
      console.log(`ℹ️  无需修复: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ 修复失败: ${filePath}`, error.message);
  }
}

// 使用示例
fixLineHeight('src/pages/lanhu_shouyexiangmuliebiao/assets/style/index.rpx.scss');
```

## 检测脚本

用于检测哪些文件存在行高问题：

```javascript
// detect-line-height-issues.js
const fs = require('fs');
const path = require('path');
const glob = require('glob');

function detectIssues() {
  const pattern = 'src/pages/**/assets/style/*.rpx.scss';
  const files = glob.sync(pattern);

  const issues = [];

  files.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const problematicLines = [];

      const lines = content.split('\n');
      lines.forEach((line, index) => {
        if (/line-height:\s*(200|300)rpx/.test(line)) {
          problematicLines.push({
            lineNumber: index + 1,
            content: line.trim()
          });
        }
      });

      if (problematicLines.length > 0) {
        issues.push({
          file,
          issues: problematicLines
        });
      }
    } catch (error) {
      console.error(`读取文件失败: ${file}`, error.message);
    }
  });

  return issues;
}

// 运行检测
const issues = detectIssues();
if (issues.length > 0) {
  console.log('🔍 发现以下文件存在行高问题：\n');
  issues.forEach(({ file, issues }) => {
    console.log(`📄 ${file}`);
    issues.forEach(({ lineNumber, content }) => {
      console.log(`   第${lineNumber}行: ${content}`);
    });
    console.log('');
  });
} else {
  console.log('✅ 未发现行高问题');
}
```

## 注意事项

1. 修复时要保持行高与字体大小的合理比例
2. 不同字体可能需要不同的行高调整
3. 修复后要全面测试页面在不同设备上的显示效果
4. 建议分批修复，避免一次性修改过多文件导致问题难以定位
5. 使用脚本修复后，仍需要手动检查和微调部分样式
6. 建议在修复前创建Git分支，便于回滚和对比
