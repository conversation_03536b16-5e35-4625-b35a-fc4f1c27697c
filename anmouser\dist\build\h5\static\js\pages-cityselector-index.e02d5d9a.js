(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-cityselector-index"],{"0433":function(e,t,a){"use strict";a("7a82");var i=a("4ea4");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("96cf");var o=i(a("2909")),n=i(a("3835")),r=i(a("1da1"));a("d9e2"),a("d401"),a("99af"),a("7db0"),a("4160"),a("caad"),a("d81d"),a("14d9"),a("4e82"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("b64b"),a("d3b7"),a("ac1f"),a("2532"),a("5319"),a("498a"),a("159b");var c=a("e429");t.default={data:function(){return{constants:{},cities:[],allCities:[],loading:!1,refreshing:!1,currentPage:1,pageSize:100,total:0,selectedCity:null,scrollIntoView:"",searchKeyword:"",searchTimer:null,isSearching:!1,isLocating:!1}},computed:{groupedCities:function(){var e=this.isSearching?this.cities:this.allCities;if(!e.length)return[];var t={};return e.forEach((function(e){var a=e.initial||"#";t[a]||(t[a]=[]),t[a].push(e)})),Object.keys(t).sort().map((function(e){return{letter:e,cities:t[e]}}))},alphabetList:function(){return this.groupedCities.map((function(e){return e.letter}))}},onLoad:function(){this.getCities();var e=uni.getStorageSync("selectedCity");e&&(this.selectedCity=e)},methods:{getCities:function(){var e=this;return(0,r.default)(regeneratorRuntime.mark((function t(){var a,i,o,r,c,l;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.prev=1,t.next=2,uni.request({url:"http://localhost:8000/api/v1/cities/?page=".concat(e.currentPage,"&page_size=").concat(e.pageSize),method:"GET",header:{"Content-Type":"application/json"}});case 2:if(a=t.sent,i=(0,n.default)(a,2),o=i[0],r=i[1],!o){t.next=3;break}throw new Error("请求错误: ".concat(o.message||o));case 3:if(200!==r.statusCode||!r.data){t.next=4;break}c=r.data,c.success&&c.data?(e.allCities=c.data,e.cities=c.data,e.total=c.total||0,console.log("获取城市数据成功:",{count:e.cities.length,total:e.total,page:c.page,pageSize:c.page_size})):(console.error("API返回错误:",c.message||"未知错误"),uni.showToast({title:c.message||"获取数据失败",icon:"none"})),t.next=5;break;case 4:throw new Error("请求失败: ".concat(r.statusCode));case 5:t.next=7;break;case 6:t.prev=6,l=t["catch"](1),console.error("获取城市数据失败:",l),uni.showToast({title:"获取城市数据失败",icon:"none"});case 7:return t.prev=7,e.loading=!1,t.finish(7);case 8:case"end":return t.stop()}}),t,null,[[1,6,7,8]])})))()},selectCity:function(e){console.log("选择的城市:",e),this.selectedCity=e,uni.setStorageSync("selectedCity",e),uni.showToast({title:"已选择: ".concat(e.name),icon:"success"})},clearSelectedCity:function(){this.selectedCity=null,uni.removeStorageSync("selectedCity"),uni.showToast({title:"已清除选择",icon:"success"})},handleLocationAction:function(){this.isLocating||(this.selectedCity?this.clearSelectedCity():this.getCurrentLocation())},scrollToLetter:function(e){var t=this;this.scrollIntoView="letter-".concat(e),setTimeout((function(){t.scrollIntoView=""}),500)},onRefresh:function(){var e=this;return(0,r.default)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.refreshing=!0,e.currentPage=1,t.prev=1,t.next=2,e.getCities();case 2:return t.prev=2,e.refreshing=!1,t.finish(2);case 3:case"end":return t.stop()}}),t,null,[[1,,2,3]])})))()},loadMore:function(){var e=this;return(0,r.default)(regeneratorRuntime.mark((function t(){var a,i,r,c,l,s,g;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!(e.loading||e.refreshing||e.cities.length>=e.total||e.isSearching)){t.next=1;break}return t.abrupt("return");case 1:return e.currentPage++,e.loading=!0,t.prev=2,t.next=3,uni.request({url:"http://localhost:8000/api/v1/cities/?page=".concat(e.currentPage,"&page_size=").concat(e.pageSize),method:"GET",header:{"Content-Type":"application/json"}});case 3:if(a=t.sent,i=(0,n.default)(a,2),r=i[0],c=i[1],!r){t.next=4;break}throw new Error("请求错误: ".concat(r.message||r));case 4:200===c.statusCode&&c.data&&(l=c.data,l.success&&l.data?(s=l.data,e.allCities=[].concat((0,o.default)(e.allCities),(0,o.default)(s)),e.cities=[].concat((0,o.default)(e.cities),(0,o.default)(s)),e.total=l.total||e.total,console.log("加载更多数据成功:",{newCount:s.length,totalCount:e.cities.length,total:e.total})):(console.error("加载更多数据API返回错误:",l.message||"未知错误"),uni.showToast({title:l.message||"加载失败",icon:"none"}))),t.next=6;break;case 5:t.prev=5,g=t["catch"](2),console.error("加载更多数据失败:",g),e.currentPage--;case 6:return t.prev=6,e.loading=!1,t.finish(6);case 7:case"end":return t.stop()}}),t,null,[[2,5,6,7]])})))()},onSearchInput:function(){var e=this;this.searchTimer&&clearTimeout(this.searchTimer),this.searchTimer=setTimeout((function(){e.performSearch()}),300)},onSearchConfirm:function(){this.searchTimer&&clearTimeout(this.searchTimer),this.performSearch()},performSearch:function(){var e=this;return(0,r.default)(regeneratorRuntime.mark((function t(){var a,i,o,r,c,l,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.searchKeyword.trim(),a){t.next=1;break}return e.isSearching=!1,e.cities=e.allCities,t.abrupt("return");case 1:return e.isSearching=!0,e.loading=!0,t.prev=2,t.next=3,uni.request({url:"http://localhost:8000/api/v1/cities/?search=".concat(encodeURIComponent(a),"&page_size=50"),method:"GET",header:{"Content-Type":"application/json"}});case 3:if(i=t.sent,o=(0,n.default)(i,2),r=o[0],c=o[1],!r){t.next=4;break}throw new Error("搜索请求错误: ".concat(r.message||r));case 4:if(200!==c.statusCode||!c.data){t.next=5;break}l=c.data,l.success&&l.data?(e.cities=l.data,console.log("搜索城市成功:",{keyword:a,count:e.cities.length}),0===e.cities.length&&uni.showToast({title:"未找到匹配的城市",icon:"none"})):(console.error("搜索API返回错误:",l.message||"未知错误"),uni.showToast({title:l.message||"搜索失败",icon:"none"})),t.next=6;break;case 5:throw new Error("搜索请求失败: ".concat(c.statusCode));case 6:t.next=8;break;case 7:t.prev=7,s=t["catch"](2),console.error("搜索城市失败:",s),uni.showToast({title:"搜索失败，请重试",icon:"none"});case 8:return t.prev=8,e.loading=!1,t.finish(8);case 9:case"end":return t.stop()}}),t,null,[[2,7,8,9]])})))()},clearSearch:function(){this.searchKeyword="",this.isSearching=!1,this.cities=this.allCities,this.searchTimer&&clearTimeout(this.searchTimer)},getCurrentLocation:function(){var e=this;return(0,r.default)(regeneratorRuntime.mark((function t(){var a,i,o,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLocating){t.next=1;break}return t.abrupt("return");case 1:if(e.isLocating=!0,t.prev=2,uni.showLoading({title:"正在定位...",mask:!0}),a=uni.getSystemInfoSync().platform,i="h5"===a||"undefined"!==typeof window,!i){t.next=4;break}return t.next=3,e.getH5Location();case 3:o=t.sent,t.next=6;break;case 4:return t.next=5,e.getUniLocation();case 5:o=t.sent;case 6:if(!o){t.next=7;break}return t.next=7,e.getCityByLocation(o.latitude,o.longitude);case 7:t.next=9;break;case 8:t.prev=8,n=t["catch"](2),console.error("定位失败:",n),e.handleLocationError(n);case 9:return t.prev=9,e.isLocating=!1,uni.hideLoading(),t.finish(9);case 10:case"end":return t.stop()}}),t,null,[[2,8,9,10]])})))()},getH5Location:function(){return new Promise((function(e,t){navigator.geolocation?navigator.geolocation.getCurrentPosition((function(t){e({latitude:t.coords.latitude,longitude:t.coords.longitude})}),(function(e){var a="定位失败";switch(e.code){case e.PERMISSION_DENIED:a="用户拒绝了定位请求";break;case e.POSITION_UNAVAILABLE:a="位置信息不可用";break;case e.TIMEOUT:a="定位请求超时";break}t(new Error(a))}),{enableHighAccuracy:!0,timeout:1e4,maximumAge:6e4}):t(new Error("浏览器不支持定位功能"))}))},getUniLocation:function(){return new Promise((function(e,t){uni.getLocation({type:"gcj02",success:function(t){e({latitude:t.latitude,longitude:t.longitude})},fail:function(e){var a="定位失败";e.errMsg&&(e.errMsg.includes("auth deny")?a="用户拒绝了定位权限":e.errMsg.includes("timeout")?a="定位超时，请重试":e.errMsg.includes("system deny")&&(a="系统拒绝定位，请检查设置")),t(new Error(a))}})}))},getCityByLocation:function(e,t){var a=this;return(0,r.default)(regeneratorRuntime.mark((function i(){var o,n,r,c,l;return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,o=null,i.prev=1,i.next=2,a.getTencentCityInfo(e,t);case 2:o=i.sent,i.next=4;break;case 3:i.prev=3,c=i["catch"](1),console.warn("腾讯地图API调用失败，使用备用方案:",c),o=a.estimateCityByCoordinates(e,t);case 4:if(!o){i.next=5;break}n=a.findCityInList(o),n?(a.selectedCity=n,uni.setStorageSync("selectedCity",n),uni.showToast({title:"定位成功: ".concat(n.name),icon:"success"})):(r={code:"000000",name:o,province:"未知省份",pinyin:"",initial:o.charAt(0).toUpperCase()},a.selectedCity=r,uni.setStorageSync("selectedCity",r),uni.showToast({title:"定位成功: ".concat(o),icon:"success"})),i.next=6;break;case 5:throw new Error("无法获取城市信息");case 6:i.next=8;break;case 7:i.prev=7,l=i["catch"](0),console.error("获取城市信息失败:",l),uni.showToast({title:"获取城市信息失败",icon:"none"});case 8:case"end":return i.stop()}}),i,null,[[0,7],[1,3]])})))()},getTencentCityInfo:function(e,t){return(0,r.default)(regeneratorRuntime.mark((function a(){var i,o,r,l,s,g,d,h,u,f;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if((0,c.isApiKeyConfigured)("TENCENT")){a.next=1;break}throw new Error("请在 config/map-config.js 中配置腾讯地图API密钥");case 1:return i=c.MAP_CONFIG.TENCENT,o="".concat(i.GEOCODER_URL,"?location=").concat(e,",").concat(t,"&key=").concat(i.KEY,"&get_poi=0"),a.prev=2,a.next=3,uni.request({url:o,method:"GET",timeout:1e4});case 3:if(r=a.sent,l=(0,n.default)(r,2),s=l[0],g=l[1],!s){a.next=4;break}throw new Error("请求失败: ".concat(s.message||s));case 4:if(200!==g.statusCode||!g.data){a.next=7;break}if(d=g.data,0!==d.status||!d.result){a.next=5;break}return h=d.result.address_component,u=h.city||h.district||"未知城市",a.abrupt("return",u.replace("市","")+"市");case 5:throw new Error("API返回错误: ".concat(d.message||"未知错误"));case 6:a.next=8;break;case 7:throw new Error("HTTP错误: ".concat(g.statusCode));case 8:a.next=10;break;case 9:throw a.prev=9,f=a["catch"](2),console.error("腾讯地图API调用失败:",f),f;case 10:case"end":return a.stop()}}),a,null,[[2,9]])})))()},getAmapCityInfo:function(e,t){return(0,r.default)(regeneratorRuntime.mark((function a(){var i,o,r,l,s,g,d,h,u,f;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if((0,c.isApiKeyConfigured)("AMAP")){a.next=1;break}throw new Error("请在 config/map-config.js 中配置高德地图API密钥");case 1:return i=c.MAP_CONFIG.AMAP,o="".concat(i.GEOCODER_URL,"?location=").concat(t,",").concat(e,"&key=").concat(i.KEY,"&radius=1000&extensions=base"),a.prev=2,a.next=3,uni.request({url:o,method:"GET",timeout:1e4});case 3:if(r=a.sent,l=(0,n.default)(r,2),s=l[0],g=l[1],!s){a.next=4;break}throw new Error("请求失败: ".concat(s.message||s));case 4:if(200!==g.statusCode||!g.data){a.next=7;break}if(d=g.data,"1"!==d.status||!d.regeocode){a.next=5;break}return h=d.regeocode.addressComponent,u=h.city||h.district||"未知城市",a.abrupt("return",u.replace("市","")+"市");case 5:throw new Error("API返回错误: ".concat(d.info||"未知错误"));case 6:a.next=8;break;case 7:throw new Error("HTTP错误: ".concat(g.statusCode));case 8:a.next=10;break;case 9:throw a.prev=9,f=a["catch"](2),console.error("高德地图API调用失败:",f),f;case 10:case"end":return a.stop()}}),a,null,[[2,9]])})))()},estimateCityByCoordinates:function(e,t){for(var a=[{name:"北京市",lat:[39.4,41.1],lng:[115.4,117.5]},{name:"上海市",lat:[30.7,31.9],lng:[120.9,122]},{name:"广州市",lat:[22.5,23.9],lng:[112.9,114.5]},{name:"深圳市",lat:[22.4,22.9],lng:[113.7,114.6]},{name:"杭州市",lat:[29.8,30.6],lng:[119.7,120.9]},{name:"成都市",lat:[30.1,31.4],lng:[103.5,104.9]},{name:"重庆市",lat:[28.1,32.2],lng:[105.2,110.2]},{name:"武汉市",lat:[29.9,31.4],lng:[113.7,115.1]},{name:"西安市",lat:[33.7,34.8],lng:[107.9,109.5]},{name:"南京市",lat:[31.1,32.9],lng:[118.2,119.2]}],i=0,o=a;i<o.length;i++){var n=o[i];if(e>=n.lat[0]&&e<=n.lat[1]&&t>=n.lng[0]&&t<=n.lng[1])return n.name}return"未知城市"},findCityInList:function(e){return this.allCities.find((function(t){return t.name===e||t.name.includes(e.replace("市",""))||e.includes(t.name.replace("市",""))}))},handleLocationError:function(e){var t=this,a=e.message||"定位失败";uni.showModal({title:"定位失败",content:"".concat(a,"\n\n您可以：\n1. 检查定位权限设置\n2. 手动搜索选择城市\n3. 稍后重试"),showCancel:!0,cancelText:"手动选择",confirmText:"重试",success:function(e){e.confirm?setTimeout((function(){t.getCurrentLocation()}),1e3):t.$nextTick((function(){}))}})}}}},"05e7":function(e,t,a){"use strict";a.r(t);var i=a("b17f"),o=a("4c8b");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("1b0f");var r,c=a("f0c5"),l=Object(c["a"])(o["default"],i["b"],i["c"],!1,null,"5db5e2aa",null,!1,i["a"],r);t["default"]=l.exports},"1b0f":function(e,t,a){"use strict";var i=a("e19f"),o=a.n(i);o.a},"1da1":function(e,t,a){"use strict";function i(e,t,a,i,o,n,r){try{var c=e[n](r),l=c.value}catch(s){return void a(s)}c.done?t(l):Promise.resolve(l).then(i,o)}function o(e){return function(){var t=this,a=arguments;return new Promise((function(o,n){var r=e.apply(t,a);function c(e){i(r,o,n,c,l,"next",e)}function l(e){i(r,o,n,c,l,"throw",e)}c(void 0)}))}}a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=o,a("d3b7")},2909:function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;var i=c(a("6005")),o=c(a("db90")),n=c(a("06c5")),r=c(a("3427"));function c(e){return e&&e.__esModule?e:{default:e}}function l(e){return(0,i.default)(e)||(0,o.default)(e)||(0,n.default)(e)||(0,r.default)()}},3427:function(e,t,a){"use strict";function i(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=i,a("d9e2"),a("d401")},"4c8b":function(e,t,a){"use strict";a.r(t);var i=a("0433"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},6005:function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=n;var i=o(a("6b75"));function o(e){return e&&e.__esModule?e:{default:e}}function n(e){if(Array.isArray(e))return(0,i.default)(e)}},"639e":function(e,t,a){var i=a("4bad");t=i(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */body *[data-v-5db5e2aa]{box-sizing:border-box;-webkit-flex-shrink:0;flex-shrink:0}body[data-v-5db5e2aa]{font-family:PingFangSC-Regular,Roboto,Helvetica Neue,Helvetica,Tahoma,Arial,PingFang SC-Light,Microsoft YaHei}uni-button[data-v-5db5e2aa]{margin:0;padding:0;border:1px solid transparent;outline:none;background-color:initial}uni-button[data-v-5db5e2aa]:active{opacity:.6}.flex-col[data-v-5db5e2aa]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column}.flex-row[data-v-5db5e2aa]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-webkit-flex-direction:row;flex-direction:row}.justify-start[data-v-5db5e2aa]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:start;-webkit-justify-content:flex-start;justify-content:flex-start}.justify-center[data-v-5db5e2aa]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center}.justify-end[data-v-5db5e2aa]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end}.justify-evenly[data-v-5db5e2aa]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:space-evenly;-webkit-justify-content:space-evenly;justify-content:space-evenly}.justify-around[data-v-5db5e2aa]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-justify-content:space-around;justify-content:space-around}.justify-between[data-v-5db5e2aa]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between}.align-start[data-v-5db5e2aa]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;align-items:flex-start}.align-center[data-v-5db5e2aa]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.align-end[data-v-5db5e2aa]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:end;-webkit-align-items:flex-end;align-items:flex-end}.page[data-v-5db5e2aa]{background-color:#f7f8fa;position:relative;width:%?750?%;height:%?2290?%;overflow:hidden}.page .box_1[data-v-5db5e2aa]{background-color:#fff;height:%?176?%;width:%?750?%}.page .box_1 .box_2[data-v-5db5e2aa]{width:%?696?%;height:%?40?%;margin:%?24?% 0 0 %?28?%}.page .box_1 .box_2 .text_1[data-v-5db5e2aa]{width:%?108?%;height:%?40?%;overflow-wrap:break-word;color:#000;font-size:%?28?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?40?%}.page .box_1 .box_2 .block_1[data-v-5db5e2aa]{background-color:#000;width:%?34?%;height:%?22?%;margin:%?10?% 0 0 %?454?%}.page .box_1 .box_2 .block_2[data-v-5db5e2aa]{background-color:#000;width:%?32?%;height:%?22?%;margin:%?10?% 0 0 %?10?%}.page .box_1 .box_2 .image_1[data-v-5db5e2aa]{width:%?50?%;height:%?24?%;margin:%?10?% 0 0 %?8?%}.page .box_1 .box_3[data-v-5db5e2aa]{width:%?416?%;height:%?36?%;margin:%?50?% 0 %?26?% %?44?%}.page .box_1 .box_3 .thumbnail_1[data-v-5db5e2aa]{width:%?18?%;height:%?32?%;margin-top:%?4?%}.page .box_1 .box_3 .text_2[data-v-5db5e2aa]{width:%?170?%;height:%?36?%;overflow-wrap:break-word;color:#000;font-size:%?34?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?36?%}.page .box_4[data-v-5db5e2aa]{position:relative;width:%?750?%;height:%?2116?%;margin-bottom:%?2?%}.page .box_4 .group_1[data-v-5db5e2aa]{background-color:#fff;height:%?108?%;width:%?750?%}.page .box_4 .group_1 .section_1[data-v-5db5e2aa]{background-color:#f7f8fa;border-radius:17px;width:%?686?%;height:%?68?%;margin:%?20?% 0 0 %?32?%}.page .box_4 .group_1 .section_1 .image-text_1[data-v-5db5e2aa]{width:%?638?%;height:%?44?%;margin:%?12?% 0 0 %?24?%}.page .box_4 .group_1 .section_1 .image-text_1 .thumbnail_2[data-v-5db5e2aa]{width:%?32?%;height:%?32?%;margin-top:%?6?%}.page .box_4 .group_1 .section_1 .image-text_1 .text-group_1[data-v-5db5e2aa]{width:%?598?%;height:%?44?%;overflow-wrap:break-word;color:#c8c9cc;font-size:%?28?%;font-family:Source Han Sans CN-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%}.page .box_4 .text_3[data-v-5db5e2aa]{width:%?112?%;height:%?44?%;overflow-wrap:break-word;color:#222;font-size:%?28?%;font-family:Source Han Sans CN-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%;margin:%?22?% 0 0 %?24?%}.page .box_4 .group_2[data-v-5db5e2aa]{background-color:#fff;border-radius:10px;width:%?700?%;height:%?100?%;margin:%?14?% 0 0 %?24?%}.page .box_4 .group_2 .image-text_2[data-v-5db5e2aa]{width:%?138?%;height:%?44?%;margin:%?28?% 0 0 %?24?%}.page .box_4 .group_2 .image-text_2 .thumbnail_3[data-v-5db5e2aa]{width:%?38?%;height:%?38?%;margin-top:%?2?%}.page .box_4 .group_2 .image-text_2 .text-group_2[data-v-5db5e2aa]{width:%?84?%;height:%?44?%;overflow-wrap:break-word;color:#222;font-size:%?28?%;font-family:Source Han Sans CN-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%}.page .box_4 .group_2 .image-text_3[data-v-5db5e2aa]{width:%?142?%;height:%?44?%;margin:%?28?% %?24?% 0 0}.page .box_4 .group_2 .image-text_3 .thumbnail_4[data-v-5db5e2aa]{width:%?28?%;height:%?28?%;margin-top:%?8?%}.page .box_4 .group_2 .image-text_3 .text-group_3[data-v-5db5e2aa]{width:%?96?%;height:%?44?%;overflow-wrap:break-word;color:#0bce94;font-size:%?24?%;font-family:Source Han Sans CN-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%}.page .box_4 .text_4[data-v-5db5e2aa]{width:%?686?%;height:%?64?%;overflow-wrap:break-word;color:#333;font-size:%?28?%;font-family:PingFang SC-Medium;font-weight:500;text-align:left;line-height:%?64?%;margin:%?26?% 0 0 %?32?%}.page .box_4 .group_3[data-v-5db5e2aa]{background-color:#fff;width:%?750?%;height:%?110?%}.page .box_4 .group_3 .text_5[data-v-5db5e2aa]{width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_3 .image_2[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_4[data-v-5db5e2aa]{background-color:#fff;height:%?110?%;width:%?750?%}.page .box_4 .group_4 .text-wrapper_1[data-v-5db5e2aa]{width:%?686?%;height:%?44?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_4 .text-wrapper_1 .text_6[data-v-5db5e2aa]{width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%}.page .box_4 .group_4 .box_5[data-v-5db5e2aa]{width:%?702?%;height:%?28?%;margin:%?8?% 0 %?2?% %?32?%}.page .box_4 .group_4 .box_5 .image_3[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin-top:%?24?%}.page .box_4 .group_4 .box_5 .text_7[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#57c051;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%;margin-left:%?-8?%}.page .box_4 .group_5[data-v-5db5e2aa]{background-color:#fff;width:%?750?%;height:%?110?%}.page .box_4 .group_5 .text_8[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%;margin:%?2?% 0 0 %?710?%}.page .box_4 .group_5 .section_2[data-v-5db5e2aa]{position:relative;width:%?702?%;height:%?56?%;margin-left:%?32?%}.page .box_4 .group_5 .section_2 .text-wrapper_2[data-v-5db5e2aa]{width:%?24?%;height:%?56?%;margin-left:%?678?%}.page .box_4 .group_5 .section_2 .text-wrapper_2 .text_9[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_5 .section_2 .text-wrapper_2 .text_10[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_5 .section_2 .text_11[data-v-5db5e2aa]{position:absolute;left:0;top:%?2?%;width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%}.page .box_4 .group_5 .section_3[data-v-5db5e2aa]{width:%?702?%;height:%?28?%;margin:0 0 %?4?% %?32?%}.page .box_4 .group_5 .section_3 .image_4[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin-top:%?22?%}.page .box_4 .group_5 .section_3 .text_12[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%;margin-left:%?-8?%}.page .box_4 .group_6[data-v-5db5e2aa]{background-color:#fff;width:%?750?%;height:%?110?%}.page .box_4 .group_6 .text_13[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%;margin:%?4?% 0 0 %?710?%}.page .box_4 .group_6 .box_6[data-v-5db5e2aa]{position:relative;width:%?702?%;height:%?56?%;margin-left:%?32?%}.page .box_4 .group_6 .box_6 .text-wrapper_3[data-v-5db5e2aa]{width:%?24?%;height:%?56?%;margin-left:%?678?%}.page .box_4 .group_6 .box_6 .text-wrapper_3 .text_14[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_6 .box_6 .text-wrapper_3 .text_15[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_6 .box_6 .text_16[data-v-5db5e2aa]{position:absolute;left:0;top:0;width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%}.page .box_4 .group_6 .image_5[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?20?% 0 0 %?32?%}.page .box_4 .group_7[data-v-5db5e2aa]{background-color:#fff;position:relative;width:%?750?%;height:%?110?%}.page .box_4 .group_7 .group_8[data-v-5db5e2aa]{position:relative;width:%?702?%;height:%?58?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_7 .group_8 .text-wrapper_4[data-v-5db5e2aa]{width:%?24?%;height:%?56?%;margin:%?2?% 0 0 %?678?%}.page .box_4 .group_7 .group_8 .text-wrapper_4 .text_17[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_7 .group_8 .text-wrapper_4 .text_18[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_7 .group_8 .text_19[data-v-5db5e2aa]{position:absolute;left:0;top:0;width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%}.page .box_4 .group_7 .image_6[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?18?% 0 0 %?32?%}.page .box_4 .group_7 .text_20[data-v-5db5e2aa]{position:absolute;left:%?710?%;top:%?6?%;width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_9[data-v-5db5e2aa]{background-color:#fff;position:relative;width:%?750?%;height:%?110?%}.page .box_4 .group_9 .group_10[data-v-5db5e2aa]{position:relative;width:%?702?%;height:%?60?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_9 .group_10 .text-wrapper_5[data-v-5db5e2aa]{width:%?24?%;height:%?56?%;margin:%?4?% 0 0 %?678?%}.page .box_4 .group_9 .group_10 .text-wrapper_5 .text_21[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_9 .group_10 .text-wrapper_5 .text_22[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_9 .group_10 .text_23[data-v-5db5e2aa]{position:absolute;left:0;top:0;width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%}.page .box_4 .group_9 .image_7[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?16?% 0 0 %?32?%}.page .box_4 .group_9 .text_24[data-v-5db5e2aa]{position:absolute;left:%?710?%;top:%?8?%;width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_11[data-v-5db5e2aa]{background-color:#fff;position:relative;width:%?750?%;height:%?110?%}.page .box_4 .group_11 .box_7[data-v-5db5e2aa]{position:relative;width:%?702?%;height:%?62?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_11 .box_7 .text-wrapper_6[data-v-5db5e2aa]{width:%?24?%;height:%?56?%;margin:%?6?% 0 0 %?678?%}.page .box_4 .group_11 .box_7 .text-wrapper_6 .text_25[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_11 .box_7 .text-wrapper_6 .text_26[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_11 .box_7 .text_27[data-v-5db5e2aa]{position:absolute;left:0;top:0;width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%}.page .box_4 .group_11 .image_8[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?14?% 0 0 %?32?%}.page .box_4 .group_11 .text_28[data-v-5db5e2aa]{position:absolute;left:%?710?%;top:%?10?%;width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_12[data-v-5db5e2aa]{background-color:#fff;height:%?110?%;width:%?750?%}.page .box_4 .group_12 .box_8[data-v-5db5e2aa]{position:relative;width:%?702?%;height:%?84?%;margin:%?12?% 0 0 %?32?%}.page .box_4 .group_12 .box_8 .text-wrapper_7[data-v-5db5e2aa]{width:%?24?%;height:%?84?%;margin-left:%?678?%}.page .box_4 .group_12 .box_8 .text-wrapper_7 .text_29[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_12 .box_8 .text-wrapper_7 .text_30[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_12 .box_8 .text-wrapper_7 .text_31[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .group_12 .box_8 .text_32[data-v-5db5e2aa]{position:absolute;left:0;top:%?20?%;width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%}.page .box_4 .group_12 .image-wrapper_1[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?12?% 0 0 %?32?%}.page .box_4 .group_12 .image-wrapper_1 .image_9[data-v-5db5e2aa]{width:%?686?%;height:%?2?%}.page .box_4 .text_33[data-v-5db5e2aa]{width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%;margin:%?-14?% 0 0 %?710?%}.page .box_4 .text_34[data-v-5db5e2aa]{width:%?686?%;height:%?64?%;overflow-wrap:break-word;color:#333;font-size:%?28?%;font-family:PingFang SC-Medium;font-weight:500;text-align:left;line-height:%?64?%;margin:%?2?% 0 0 %?32?%}.page .box_4 .group_13[data-v-5db5e2aa]{background-color:#fff;width:%?750?%;height:%?110?%}.page .box_4 .group_13 .text_35[data-v-5db5e2aa]{width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_13 .image_10[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .image_11[data-v-5db5e2aa]{width:%?750?%;height:%?68?%;margin-top:%?90?%}.page .box_4 .group_14[data-v-5db5e2aa]{background-color:#fff;width:%?750?%;height:%?110?%;margin-top:%?62?%}.page .box_4 .group_14 .text_36[data-v-5db5e2aa]{width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_14 .image_12[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_15[data-v-5db5e2aa]{background-color:#fff;width:%?750?%;height:%?110?%}.page .box_4 .group_15 .text_37[data-v-5db5e2aa]{width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_15 .image_13[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_16[data-v-5db5e2aa]{background-color:#fff;width:%?750?%;height:%?110?%}.page .box_4 .group_16 .text_38[data-v-5db5e2aa]{width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_16 .image_14[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_17[data-v-5db5e2aa]{background-color:#fff;width:%?750?%;height:%?110?%;margin-bottom:%?8?%}.page .box_4 .group_17 .text_39[data-v-5db5e2aa]{width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_17 .image_15[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_18[data-v-5db5e2aa]{background-color:#fff;position:absolute;left:0;top:%?1448?%;width:%?750?%;height:%?110?%}.page .box_4 .group_18 .text_40[data-v-5db5e2aa]{width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_18 .image_16[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_19[data-v-5db5e2aa]{background-color:#fff;position:absolute;left:0;top:%?1558?%;width:%?750?%;height:%?110?%}.page .box_4 .group_19 .text_41[data-v-5db5e2aa]{width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_19 .image_17[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_20[data-v-5db5e2aa]{background-color:#fff;position:absolute;left:0;top:%?2108?%;width:%?750?%;height:%?110?%}.page .box_4 .group_20 .text_42[data-v-5db5e2aa]{width:%?686?%;height:%?44?%;overflow-wrap:break-word;color:#333;font-size:%?26?%;font-family:PingFang SC-Regular;font-weight:NaN;text-align:left;white-space:nowrap;line-height:%?44?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .group_20 .image_18[data-v-5db5e2aa]{width:%?686?%;height:%?2?%;margin:%?32?% 0 0 %?32?%}.page .box_4 .text_43[data-v-5db5e2aa]{position:absolute;left:%?710?%;top:%?796?%;width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .text_44[data-v-5db5e2aa]{position:absolute;left:%?710?%;top:%?908?%;width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .text_45[data-v-5db5e2aa]{position:absolute;left:%?710?%;top:%?1020?%;width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .text_46[data-v-5db5e2aa]{position:absolute;left:%?710?%;top:%?1132?%;width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}.page .box_4 .text_47[data-v-5db5e2aa]{position:absolute;left:%?710?%;top:%?1272?%;width:%?24?%;height:%?28?%;overflow-wrap:break-word;color:#969799;font-size:%?20?%;font-family:PingFang SC-Medium;font-weight:500;text-align:center;white-space:nowrap;line-height:%?28?%}\n/* 城市选择相关样式 */.box_4[data-v-5db5e2aa]{height:calc(100vh - %?200?%) /* 设置滚动区域高度 */}\n/* 搜索框样式 */.search-container[data-v-5db5e2aa]{-webkit-box-align:center;-webkit-align-items:center;align-items:center;background-color:#f8f8f8;border-radius:%?12?%;padding:%?20?%;margin:%?20?%;position:relative}.search-icon[data-v-5db5e2aa]{width:%?32?%;height:%?32?%;margin-right:%?16?%;-webkit-flex-shrink:0;flex-shrink:0}.search-input[data-v-5db5e2aa]{-webkit-box-flex:1;-webkit-flex:1;flex:1;font-size:%?28?%;color:#333;background-color:initial;border:none;outline:none}.search-input[data-v-5db5e2aa]::-webkit-input-placeholder{color:#999}.search-input[data-v-5db5e2aa]::placeholder{color:#999}.clear-btn[data-v-5db5e2aa]{width:%?40?%;height:%?40?%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;background-color:#ccc;border-radius:50%;margin-left:%?16?%}.clear-btn[data-v-5db5e2aa]:active{background-color:#999}.clear-text[data-v-5db5e2aa]{color:#fff;font-size:%?32?%;font-weight:700;line-height:1}.city-groups[data-v-5db5e2aa]{position:relative;padding:%?20?%;padding-right:%?80?% /* 为右侧字母索引留出空间 */}\n/* 右侧字母索引 */.alphabet-index[data-v-5db5e2aa]{position:fixed;right:%?20?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;align-items:center;z-index:100}.alphabet-item[data-v-5db5e2aa]{font-size:%?24?%;color:#666;padding:%?8?% %?12?%;margin:%?2?% 0;background-color:hsla(0,0%,100%,.8);border-radius:%?6?%;text-align:center;min-width:%?40?%}.alphabet-item[data-v-5db5e2aa]:active{background-color:#007aff;color:#fff}\n/* 城市分组 */.city-group[data-v-5db5e2aa]{margin-bottom:%?40?%}.group-letter[data-v-5db5e2aa]{font-size:%?32?%;color:#333;font-weight:700;margin-bottom:%?20?%;padding-left:%?10?%}.group-city-item[data-v-5db5e2aa]{padding:%?25?% 0;border-bottom:%?1?% solid #f0f0f0}.group-city-item[data-v-5db5e2aa]:active{background-color:#f5f5f5}.group-city-item[data-v-5db5e2aa]:last-child{border-bottom:none}.city-text[data-v-5db5e2aa]{font-size:%?30?%;color:#333;margin-bottom:%?15?%;padding-left:%?10?%}.city-divider[data-v-5db5e2aa]{width:100%;height:%?2?%}.loading-container[data-v-5db5e2aa]{padding:%?60?% %?20?%;text-align:center}.loading-text[data-v-5db5e2aa]{font-size:%?28?%;color:#666}.no-data-container[data-v-5db5e2aa]{padding:%?100?% %?20?%;text-align:center}.no-data-text[data-v-5db5e2aa]{font-size:%?28?%;color:#999;margin-bottom:%?30?%}.retry-btn[data-v-5db5e2aa]{padding:%?20?% %?40?%;background-color:#007aff;border-radius:%?10?%;display:inline-block}.retry-btn[data-v-5db5e2aa]:active{background-color:#0056cc}.retry-text[data-v-5db5e2aa]{color:#fff;font-size:%?28?%}\n/* 定位按钮禁用状态 */.image-text_3.disabled[data-v-5db5e2aa]{opacity:.6;pointer-events:none}.image-text_3.disabled .text-group_3[data-v-5db5e2aa]{color:#999}',""]),e.exports=t},"96cf":function(e,t){!function(t){"use strict";var a,i=Object.prototype,o=i.hasOwnProperty,n="function"===typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",c=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag",s="object"===typeof e,g=t.regeneratorRuntime;if(g)s&&(e.exports=g);else{g=t.regeneratorRuntime=s?e.exports:{},g.wrap=_;var d="suspendedStart",h="suspendedYield",u="executing",f="completed",p={},w={};w[r]=function(){return this};var b=Object.getPrototypeOf,x=b&&b(b(M([])));x&&x!==i&&o.call(x,r)&&(w=x);var v=C.prototype=y.prototype=Object.create(w);k.prototype=v.constructor=C,C.constructor=k,C[l]=k.displayName="GeneratorFunction",g.isGeneratorFunction=function(e){var t="function"===typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},g.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,C):(e.__proto__=C,l in e||(e[l]="GeneratorFunction")),e.prototype=Object.create(v),e},g.awrap=function(e){return{__await:e}},S(P.prototype),P.prototype[c]=function(){return this},g.AsyncIterator=P,g.async=function(e,t,a,i){var o=new P(_(e,t,a,i));return g.isGeneratorFunction(t)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},S(v),v[l]="Generator",v[r]=function(){return this},v.toString=function(){return"[object Generator]"},g.keys=function(e){var t=[];for(var a in e)t.push(a);return t.reverse(),function a(){while(t.length){var i=t.pop();if(i in e)return a.value=i,a.done=!1,a}return a.done=!0,a}},g.values=M,F.prototype={constructor:F,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=a,this.done=!1,this.delegate=null,this.method="next",this.arg=a,this.tryEntries.forEach(L),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=a)},stop:function(){this.done=!0;var e=this.tryEntries[0],t=e.completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function i(i,o){return c.type="throw",c.arg=e,t.next=i,o&&(t.method="next",t.arg=a),!!o}for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n],c=r.completion;if("root"===r.tryLoc)return i("end");if(r.tryLoc<=this.prev){var l=o.call(r,"catchLoc"),s=o.call(r,"finallyLoc");if(l&&s){if(this.prev<r.catchLoc)return i(r.catchLoc,!0);if(this.prev<r.finallyLoc)return i(r.finallyLoc)}else if(l){if(this.prev<r.catchLoc)return i(r.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return i(r.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&o.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var r=n?n.completion:{};return r.type=e,r.arg=t,n?(this.method="next",this.next=n.finallyLoc,p):this.complete(r)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),L(a),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var i=a.completion;if("throw"===i.type){var o=i.arg;L(a)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,i){return this.delegate={iterator:M(e),resultName:t,nextLoc:i},"next"===this.method&&(this.arg=a),p}}}function _(e,t,a,i){var o=t&&t.prototype instanceof y?t:y,n=Object.create(o.prototype),r=new F(i||[]);return n._invoke=E(e,a,r),n}function m(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(i){return{type:"throw",arg:i}}}function y(){}function k(){}function C(){}function S(e){["next","throw","return"].forEach((function(t){e[t]=function(e){return this._invoke(t,e)}}))}function P(e){function t(a,i,n,r){var c=m(e[a],e,i);if("throw"!==c.type){var l=c.arg,s=l.value;return s&&"object"===typeof s&&o.call(s,"__await")?Promise.resolve(s.__await).then((function(e){t("next",e,n,r)}),(function(e){t("throw",e,n,r)})):Promise.resolve(s).then((function(e){l.value=e,n(l)}),(function(e){return t("throw",e,n,r)}))}r(c.arg)}var a;function i(e,i){function o(){return new Promise((function(a,o){t(e,i,a,o)}))}return a=a?a.then(o,o):o()}this._invoke=i}function E(e,t,a){var i=d;return function(o,n){if(i===u)throw new Error("Generator is already running");if(i===f){if("throw"===o)throw n;return T()}a.method=o,a.arg=n;while(1){var r=a.delegate;if(r){var c=N(r,a);if(c){if(c===p)continue;return c}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(i===d)throw i=f,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);i=u;var l=m(e,t,a);if("normal"===l.type){if(i=a.done?f:h,l.arg===p)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(i=f,a.method="throw",a.arg=l.arg)}}}function N(e,t){var i=e.iterator[t.method];if(i===a){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=a,N(e,t),"throw"===t.method))return p;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return p}var o=m(i,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,p;var n=o.arg;return n?n.done?(t[e.resultName]=n.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=a),t.delegate=null,p):n:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function z(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function F(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(z,this),this.reset(!0)}function M(e){if(e){var t=e[r];if(t)return t.call(e);if("function"===typeof e.next)return e;if(!isNaN(e.length)){var i=-1,n=function t(){while(++i<e.length)if(o.call(e,i))return t.value=e[i],t.done=!1,t;return t.value=a,t.done=!0,t};return n.next=n}}return{next:T}}function T(){return{value:a,done:!0}}}(function(){return this||"object"===typeof self&&self}()||Function("return this")())},b17f:function(e,t,a){"use strict";var i;a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return i}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"page flex-col"},[a("v-uni-view",{staticClass:"box_1 flex-col"},[a("v-uni-view",{staticClass:"box_3 flex-row justify-between"},[a("v-uni-image",{staticClass:"thumbnail_1",attrs:{referrerpolicy:"no-referrer",src:"/static/cityselector/FigmaDDSSlicePNGa3fa9e448b139bfdff3a8dabbb6265ce.png"}}),a("v-uni-text",{staticClass:"text_2"},[e._v("国家和地区")])],1)],1),a("v-uni-scroll-view",{staticClass:"box_4 flex-col",attrs:{"scroll-y":"true","refresher-enabled":"true","refresher-triggered":e.refreshing,"scroll-into-view":e.scrollIntoView},on:{refresherrefresh:function(t){arguments[0]=t=e.$handleEvent(t),e.onRefresh.apply(void 0,arguments)},scrolltolower:function(t){arguments[0]=t=e.$handleEvent(t),e.loadMore.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"group_1 flex-col"},[a("v-uni-view",{staticClass:"section_1 flex-row"},[a("v-uni-view",{staticClass:"search-container flex-row",staticStyle:{"padding-top":"8px"}},[a("v-uni-image",{staticClass:"search-icon",attrs:{referrerpolicy:"no-referrer",src:"/static/cityselector/FigmaDDSSlicePNGaf3e79b7cd05c51210e7b9ad1893305c.png"}}),a("v-uni-input",{staticClass:"search-input",attrs:{type:"text",placeholder:"请输入城市名称","confirm-type":"search"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.onSearchInput.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onSearchConfirm.apply(void 0,arguments)}},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}}),e.searchKeyword?a("v-uni-view",{staticClass:"clear-btn",staticStyle:{"margin-left":"99px"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clearSearch.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"clear-text"},[e._v("×")])],1):e._e()],1)],1)],1),a("v-uni-text",{staticClass:"text_3"},[e._v("当前城市")]),a("v-uni-view",{staticClass:"group_2 flex-row justify-between"},[a("v-uni-view",{staticClass:"image-text_2 flex-row justify-between",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getCurrentLocation.apply(void 0,arguments)}}},[a("v-uni-image",{staticClass:"thumbnail_3",attrs:{referrerpolicy:"no-referrer",src:"/static/cityselector/FigmaDDSSlicePNG8ce251d7cdb03d0b79452073ad295013.png"}}),a("v-uni-text",{staticClass:"text-group_2"},[e._v(e._s(e.isLocating?"正在定位...":e.selectedCity?e.selectedCity.name:"请选择城市"))])],1),a("v-uni-view",{staticClass:"image-text_3 flex-row justify-between",class:{disabled:e.isLocating},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleLocationAction.apply(void 0,arguments)}}},[a("v-uni-image",{staticClass:"thumbnail_4",attrs:{referrerpolicy:"no-referrer",src:"/static/cityselector/FigmaDDSSlicePNG7fdcf6ebdb51682c5d9d318fb66370c9.png"}}),a("v-uni-text",{staticClass:"text-group_3"},[e._v(e._s(e.isLocating?"定位中...":e.selectedCity?"清除选择":"重新定位"))])],1)],1),e.groupedCities.length>0?a("v-uni-view",{staticClass:"city-groups"},[a("v-uni-view",{staticClass:"alphabet-index"},e._l(e.alphabetList,(function(t){return a("v-uni-text",{key:t,staticClass:"alphabet-item",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.scrollToLetter(t)}}},[e._v(e._s(t))])})),1),a("v-uni-view",{staticClass:"city-content"},e._l(e.groupedCities,(function(t){return a("v-uni-view",{key:t.letter,staticClass:"city-group",attrs:{id:"letter-"+t.letter}},[a("v-uni-text",{staticClass:"group-letter"},[e._v(e._s(t.letter))]),e._l(t.cities,(function(t,i){return a("v-uni-view",{key:t.code||i,staticClass:"group-city-item flex-col justify-end",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.selectCity(t)}}},[a("v-uni-text",{staticClass:"city-text"},[e._v(e._s(t.name))]),a("v-uni-image",{staticClass:"city-divider",attrs:{referrerpolicy:"no-referrer",src:"/static/cityselector/FigmaDDSSlicePNG917826c1fd1fcf3c0a049cf6d5026e0b.png"}})],1)}))],2)})),1)],1):e._e(),e.loading?a("v-uni-view",{staticClass:"loading-container"},[a("v-uni-text",{staticClass:"loading-text"},[e._v("正在加载城市数据...")])],1):e._e(),e.loading||0!==e.cities.length?e._e():a("v-uni-view",{staticClass:"no-data-container"},[a("v-uni-text",{staticClass:"no-data-text"},[e._v("暂无城市数据")])],1)],1)],1)},n=[]},db90:function(e,t,a){"use strict";function i(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=i,a("a4d3"),a("e01a"),a("d28b"),a("a630"),a("d3b7"),a("3ca3"),a("ddb0")},e19f:function(e,t,a){var i=a("639e");"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var o=a("4f06").default;o("5e64c0d2",i,!0,{sourceMap:!1,shadowMode:!1})},e429:function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.MAP_CONFIG=void 0,t.getAvailableProviders=n,t.isApiKeyConfigured=o,a("4de4"),a("e9f5"),a("910d"),a("b64b"),a("d3b7"),a("2ca0");var i=t.MAP_CONFIG={TENCENT:{KEY:"NHWBZ-W5MRZ-M6VX4-7ZHPF-UEVS6-PSFC3",GEOCODER_URL:"https://apis.map.qq.com/ws/geocoder/v1/"},AMAP:{KEY:"YOUR_AMAP_KEY",GEOCODER_URL:"https://restapi.amap.com/v3/geocode/regeo"},BAIDU:{KEY:"YOUR_BAIDU_MAP_KEY",GEOCODER_URL:"https://api.map.baidu.com/reverse_geocoding/v3/"}};function o(e){var t=i[e];return t&&t.KEY&&!t.KEY.startsWith("YOUR_")}function n(){return Object.keys(i).filter((function(e){return o(e)}))}}}]);