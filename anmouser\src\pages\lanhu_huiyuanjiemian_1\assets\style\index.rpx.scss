.page {
  background-color: rgba(11, 11, 11, 1);
  position: relative;
  width: 750rpx;
  height: 2144rpx;
  overflow: hidden;
  .box_1 {
    background-color: rgba(43, 47, 54, 1);
    width: 750rpx;
    height: 1256rpx;
    margin-top: 1334rpx;
  }
  .box_2 {
    position: absolute;
    left: -378rpx;
    top: -350rpx;
    width: 1374rpx;
    height: 1974rpx;
    background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG1b24b8fb2f4951617799f0f75436778b.png)
      100% no-repeat;
    background-size: 100% 100%;
    .block_1 {
      background-color: rgba(255, 255, 255, 1);
      height: 306rpx;
      width: 752rpx;
      margin: 1560rpx 0 0 376rpx;
      .text-wrapper_1 {
        width: 128rpx;
        height: 38rpx;
        margin: 36rpx 0 0 56rpx;
        .text_1 {
          width: 128rpx;
          height: 38rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 32rpx;
          font-family: Ping<PERSON>ang SC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 32rpx;
        }
      }
      .section_1 {
        width: 614rpx;
        height: 98rpx;
        margin: 26rpx 0 0 72rpx;
        .section_2 {
          background-image: linear-gradient(
            136deg,
            rgba(246, 200, 101, 1) 0,
            rgba(240, 177, 76, 1) 100%
          );
          border-radius: 50%;
          width: 98rpx;
          height: 98rpx;
        }
        .section_3 {
          background-image: linear-gradient(
            148deg,
            rgba(239, 144, 133, 1) 0,
            rgba(238, 124, 104, 1) 100%
          );
          border-radius: 50%;
          width: 98rpx;
          height: 98rpx;
          margin-left: 74rpx;
        }
        .section_4 {
          background-image: linear-gradient(
            141deg,
            rgba(112, 193, 126, 1) 0,
            rgba(78, 177, 111, 1) 100%
          );
          border-radius: 50%;
          width: 98rpx;
          height: 98rpx;
          margin-left: 74rpx;
        }
        .section_5 {
          background-image: linear-gradient(
            180deg,
            rgba(255, 185, 186, 1) 0,
            rgba(248, 161, 162, 1) 100%
          );
          border-radius: 50%;
          width: 98rpx;
          height: 98rpx;
          margin-left: 74rpx;
        }
      }
      .section_6 {
        width: 628rpx;
        height: 70rpx;
        margin: 6rpx 0 32rpx 64rpx;
        .text-wrapper_2 {
          width: 118rpx;
          height: 70rpx;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 28rpx;
          .paragraph_1 {
            width: 118rpx;
            height: 70rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 28rpx;
          }
          .text_2 {
            width: 118rpx;
            height: 70rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 24rpx;
          }
        }
        .text-wrapper_3 {
          width: 120rpx;
          height: 70rpx;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 28rpx;
          margin-left: 42rpx;
          .paragraph_2 {
            width: 120rpx;
            height: 70rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 28rpx;
          }
          .text_3 {
            width: 120rpx;
            height: 70rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 24rpx;
          }
        }
        .text-wrapper_4 {
          width: 112rpx;
          height: 70rpx;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 28rpx;
          margin-left: 74rpx;
          .paragraph_3 {
            width: 112rpx;
            height: 70rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 28rpx;
          }
          .text_4 {
            width: 112rpx;
            height: 70rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 24rpx;
          }
        }
        .text-wrapper_5 {
          width: 112rpx;
          height: 70rpx;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 28rpx;
          margin-left: 50rpx;
          .paragraph_4 {
            width: 112rpx;
            height: 70rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 28rpx;
          }
          .text_5 {
            width: 112rpx;
            height: 70rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 24rpx;
          }
        }
      }
    }
    .block_2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 20px 20px 0px 0px;
      width: 750rpx;
      height: 676rpx;
      margin: 886rpx 246rpx 0 -750rpx;
      .section_7 {
        width: 684rpx;
        height: 262rpx;
        margin: 116rpx 0 0 32rpx;
        .group_1 {
          background-color: rgba(255, 244, 233, 1);
          border-radius: 10px;
          position: relative;
          width: 220rpx;
          height: 262rpx;
          border: 2px solid rgba(216, 177, 145, 1);
          .text_6 {
            width: 112rpx;
            height: 48rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 48rpx;
            margin: 22rpx 0 0 54rpx;
          }
          .text-wrapper_6 {
            width: 120rpx;
            height: 48rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 48rpx;
            margin: 6rpx 0 0 50rpx;
            .text_7 {
              width: 120rpx;
              height: 48rpx;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 32rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 48rpx;
            }
            .text_8 {
              width: 120rpx;
              height: 48rpx;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 48rpx;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 48rpx;
            }
          }
          .text_9 {
            width: 68rpx;
            height: 48rpx;
            overflow-wrap: break-word;
            color: rgba(174, 177, 183, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            text-decoration: line-through;
            font-weight: NaN;
            text-align: left;
            line-height: 48rpx;
            margin: 10rpx 0 0 76rpx;
          }
          .group_2 {
            background-image: linear-gradient(
              90deg,
              rgba(240, 193, 170, 1) 0,
              rgba(215, 176, 143, 1) 100%
            );
            border-radius: 10px;
            width: 152rpx;
            height: 42rpx;
            margin: 10rpx 0 28rpx 34rpx;
          }
          .text_10 {
            position: absolute;
            left: 60rpx;
            top: 188rpx;
            width: 102rpx;
            height: 48rpx;
            overflow-wrap: break-word;
            color: rgba(151, 96, 58, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            line-height: 48rpx;
          }
        }
        .group_3 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 10px;
          width: 220rpx;
          height: 262rpx;
          border: 2px solid rgba(228, 228, 228, 1);
          margin-left: 12rpx;
          .text-group_1 {
            width: 140rpx;
            height: 160rpx;
            margin: 22rpx 0 0 40rpx;
            .text_11 {
              width: 140rpx;
              height: 48rpx;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 28rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 48rpx;
            }
            .text-wrapper_7 {
              width: 120rpx;
              height: 48rpx;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 48rpx;
              margin: 6rpx 0 0 10rpx;
              .text_12 {
                width: 120rpx;
                height: 48rpx;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 32rpx;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 48rpx;
              }
              .text_13 {
                width: 120rpx;
                height: 48rpx;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 48rpx;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 48rpx;
              }
            }
            .text_14 {
              width: 68rpx;
              height: 48rpx;
              overflow-wrap: break-word;
              color: rgba(174, 177, 183, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              text-decoration: line-through;
              font-weight: NaN;
              text-align: left;
              line-height: 48rpx;
              margin: 10rpx 0 0 36rpx;
            }
          }
          .text-wrapper_8 {
            background-color: rgba(219, 219, 219, 1);
            border-radius: 10px;
            height: 42rpx;
            width: 152rpx;
            margin: 6rpx 0 32rpx 34rpx;
            .text_15 {
              width: 102rpx;
              height: 48rpx;
              overflow-wrap: break-word;
              color: rgba(148, 148, 148, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              line-height: 48rpx;
              margin-left: 26rpx;
            }
          }
        }
        .group_4 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 10px;
          width: 220rpx;
          height: 262rpx;
          border: 2px solid rgba(228, 228, 228, 1);
          margin-left: 12rpx;
          .text-group_2 {
            width: 124rpx;
            height: 160rpx;
            margin: 22rpx 0 0 48rpx;
            .text_16 {
              width: 112rpx;
              height: 48rpx;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 28rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 48rpx;
              margin-left: 6rpx;
            }
            .text-wrapper_9 {
              width: 124rpx;
              height: 48rpx;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 48rpx;
              margin-top: 6rpx;
              .text_17 {
                width: 124rpx;
                height: 48rpx;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 36rpx;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 48rpx;
              }
              .text_18 {
                width: 124rpx;
                height: 48rpx;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 48rpx;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 48rpx;
              }
            }
            .text_19 {
              width: 68rpx;
              height: 48rpx;
              overflow-wrap: break-word;
              color: rgba(174, 177, 183, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              text-decoration: line-through;
              font-weight: NaN;
              text-align: left;
              line-height: 48rpx;
              margin: 10rpx 0 0 28rpx;
            }
          }
          .text-wrapper_10 {
            background-color: rgba(219, 219, 219, 1);
            border-radius: 10px;
            height: 42rpx;
            width: 152rpx;
            margin: 6rpx 0 32rpx 34rpx;
            .text_20 {
              width: 102rpx;
              height: 48rpx;
              overflow-wrap: break-word;
              color: rgba(148, 148, 148, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              line-height: 48rpx;
              margin-left: 26rpx;
            }
          }
        }
      }
      .section_8 {
        background-image: linear-gradient(
          90deg,
          rgba(43, 47, 54, 1) 0,
          rgba(101, 95, 92, 1) 100%
        );
        border-radius: 100px;
        width: 696rpx;
        height: 100rpx;
        margin: 66rpx 0 0 26rpx;
        .text-wrapper_11 {
          width: 120rpx;
          height: 48rpx;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 48rpx;
          margin: 26rpx 0 0 208rpx;
          .text_21 {
            width: 120rpx;
            height: 48rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 32rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 48rpx;
          }
          .text_22 {
            width: 120rpx;
            height: 48rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 48rpx;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 48rpx;
          }
        }
        .text_23 {
          width: 144rpx;
          height: 48rpx;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 36rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 48rpx;
          margin: 26rpx 208rpx 0 0;
        }
      }
      .section_9 {
        width: 436rpx;
        height: 48rpx;
        margin: 28rpx 0 0 150rpx;
        .group_5 {
          border-radius: 50%;
          width: 30rpx;
          height: 30rpx;
          border: 1px solid rgba(153, 153, 153, 1);
          margin-top: 8rpx;
        }
        .text-wrapper_12 {
          width: 392rpx;
          height: 48rpx;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 48rpx;
          .text_24 {
            width: 392rpx;
            height: 48rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 48rpx;
          }
          .text_25 {
            width: 392rpx;
            height: 48rpx;
            overflow-wrap: break-word;
            color: rgba(215, 175, 142, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 48rpx;
          }
        }
      }
      .section_10 {
        background-color: rgba(245, 245, 245, 1);
        width: 750rpx;
        height: 20rpx;
        margin: 30rpx 0 6rpx 0;
      }
    }
    .block_3 {
      position: absolute;
      left: 0;
      top: 0;
      width: 1374rpx;
      height: 888rpx;
      .group_6 {
        width: 688rpx;
        height: 38rpx;
        margin: 364rpx 0 0 410rpx;
        .text_26 {
          width: 64rpx;
          height: 36rpx;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: Inter-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
        .thumbnail_1 {
          width: 36rpx;
          height: 36rpx;
          margin-left: 502rpx;
        }
        .thumbnail_2 {
          width: 36rpx;
          height: 36rpx;
          margin-left: 6rpx;
        }
        .thumbnail_3 {
          width: 38rpx;
          height: 38rpx;
          margin-left: 6rpx;
        }
      }
      .group_7 {
        width: 288rpx;
        height: 288rpx;
        margin: 74rpx 0 124rpx 380rpx;
        .section_11 {
          position: relative;
          width: 288rpx;
          height: 288rpx;
          background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG48515ac40375b7c83a6436611c40671b.png)
            100% no-repeat;
          background-size: 100% 100%;
          .image-wrapper_1 {
            height: 288rpx;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG65f5b26c5fff306b2173835ab441c322.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 288rpx;
            position: absolute;
            left: 226rpx;
            top: 0;
            .image_1 {
              width: 288rpx;
              height: 288rpx;
            }
          }
          .thumbnail_4 {
            position: absolute;
            left: 34rpx;
            top: -24rpx;
            width: 18rpx;
            height: 34rpx;
          }
        }
      }
      .text-wrapper_13 {
        position: absolute;
        left: 454rpx;
        top: 446rpx;
        width: 128rpx;
        height: 44rpx;
        .text_27 {
          width: 128rpx;
          height: 44rpx;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 32rpx;
          font-family: Inter-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 44rpx;
        }
      }
      .group_8 {
        box-shadow: inset 1px 1px 0px 0px rgba(255, 255, 255, 1);
        background-image: linear-gradient(
          154deg,
          rgba(255, 250, 240, 1) 0,
          rgba(206, 158, 120, 1) 100%
        );
        border-radius: 20px;
        height: 302rpx;
        width: 686rpx;
        position: absolute;
        left: 410rpx;
        top: 540rpx;
        .box_3 {
          box-shadow: inset 1px 1px 0px 0px rgba(255, 255, 255, 1);
          background-image: linear-gradient(
            146deg,
            rgba(255, 222, 157, 1) 0,
            rgba(206, 158, 120, 1) 100%
          );
          border-radius: 20px;
          height: 302rpx;
          width: 686rpx;
          position: relative;
          overflow: hidden;

          // 白色光影渐变层
          .white-shine-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 0.4) 0%,
              rgba(255, 255, 255, 0.2) 25%,
              rgba(255, 255, 255, 0.1) 50%,
              rgba(255, 255, 255, 0.05) 75%,
              rgba(255, 255, 255, 0) 100%
            );
            border-radius: 20rpx;
            pointer-events: none; // 确保不影响点击事件
            z-index: 1;
          }

          .group_9 {
            width: 132rpx;
            height: 136rpx;
            margin: 2rpx 0 0 554rpx;
            position: relative;
            z-index: 2;
            .box_4 {
              width: 132rpx;
              height: 136rpx;
              background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGb293715fa2fe32a97708a7696cac578f.png)
                100% no-repeat;
              background-size: 100% 100%;
            }
          }
          .text-wrapper_14 {
            width: 342rpx;
            height: 24rpx;
            margin: 50rpx 0 90rpx 34rpx;
            position: relative;
            z-index: 2;
            .text_28 {
              width: 342rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(62, 29, 4, 1);
              font-size: 38rpx;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }
          .group_10 {
            background-image: linear-gradient(
              180deg,
              rgba(255, 240, 223, 1) 0,
              rgba(255, 245, 239, 0) 100%
            );
            position: absolute;
            left: 26rpx;
            top: -52rpx;
            width: 146rpx;
            height: 446rpx;
          }
          .group_11 {
            background-image: linear-gradient(
              180deg,
              rgba(255, 245, 239, 1) 0,
              rgba(255, 245, 239, 0) 100%
            );
            position: absolute;
            left: 112rpx;
            top: -246rpx;
            width: 146rpx;
            height: 566rpx;
          }
          .group_12 {
            height: 228rpx;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG577b88c99c41fa54b92d33e4e334f86b.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 250rpx;
            position: absolute;
            left: 436rpx;
            top: 38rpx;
            z-index: 2;
            .text-wrapper_15 {
              height: 60rpx;
              background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG45961fda21afd8e4b2646cfb27026d11.png)
                100% no-repeat;
              background-size: 100% 100%;
              width: 180rpx;
              margin: 132rpx 0 0 42rpx;
              .text_29 {
                width: 112rpx;
                height: 28rpx;
                overflow-wrap: break-word;
                color: rgba(92, 92, 92, 1);
                font-size: 28rpx;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 28rpx;
                margin: 16rpx 0 0 34rpx;
              }
            }
            .group_13 {
              background-color: rgba(232, 191, 155, 0.4);
              height: 70rpx;
              width: 686rpx;
              position: absolute;
              left: -436rpx;
              top: 6rpx;
              z-index: 3;
              .text_30 {
                background-image: linear-gradient(
                  146deg,
                  rgba(213, 172, 135, 1) 0,
                  rgba(185, 124, 72, 1) 100%
                );
                width: 200rpx;
                height: 40rpx;
                overflow-wrap: break-word;
                color: #000;
                font-size: 40rpx;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 40rpx;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin: 18rpx 0 0 158rpx;
              }
              .group_14 {
                position: absolute;
                left: 566rpx;
                top: -16rpx;
                width: 30rpx;
                height: 44rpx;
                background: url(/static/lanhu_huiyuanjiemian_1/6456d02b8b73459b9038769094e48df1_mergeImage.png)
                  100% no-repeat;
                background-size: 100% 100%;
              }
              .group_15 {
                position: absolute;
                left: 620rpx;
                top: -16rpx;
                width: 30rpx;
                height: 44rpx;
                background: url(/static/lanhu_huiyuanjiemian_1/b77255305c91446bbf19b6699904f13e_mergeImage.png)
                  100% no-repeat;
                background-size: 100% 100%;
              }
            }
          }
          .image-wrapper_2 {
            background-image: linear-gradient(
              179deg,
              rgba(255, 234, 193, 1) 0,
              rgba(235, 193, 162, 1) 100%
            );
            border-radius: 50%;
            height: 112rpx;
            border: 2px gradient;
            width: 112rpx;
            position: absolute;
            left: 28rpx;
            top: 24rpx;
            z-index: 3;
            .label_1 {
              width: 64rpx;
              height: 84rpx;
              margin: 14rpx 0 0 24rpx;
            }
          }
        }
      }
      .group_16 {
        background-image: linear-gradient(
          179deg,
          rgba(248, 236, 197, 1) 0,
          rgba(231, 151, 93, 1) 100%
        );
        border-radius: 50%;
        height: 70rpx;
        border: 1px gradient;
        width: 70rpx;
        position: absolute;
        left: 968rpx;
        top: 524rpx;
        .block_4 {
          background-image: linear-gradient(
            138deg,
            rgba(255, 247, 234, 1) 0,
            rgba(244, 162, 98, 1) 100%
          );
          border-radius: 2px;
          height: 62rpx;
          border: 1px gradient;
          width: 62rpx;
          margin: 6rpx 0 0 4rpx;
          .section_12 {
            height: 62rpx;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG5f34148596fd027a8f98a27ad3c7dcb3.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 62rpx;
            .group_17 {
              box-shadow: inset 1px 0px 0px 0px rgba(0, 0, 0, 0.21);
              background-image: linear-gradient(
                143deg,
                rgba(234, 164, 110, 1) 0,
                rgba(241, 171, 118, 1) 100%
              );
              border-radius: 1px;
              height: 44rpx;
              width: 44rpx;
              margin: 8rpx 0 0 8rpx;
              .box_5 {
                height: 44rpx;
                background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGee080a99c1f66756c342138bdf7118ce.png)
                  100% no-repeat;
                background-size: 100% 100%;
                width: 44rpx;
                .section_13 {
                  width: 22rpx;
                  height: 22rpx;
                  background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGc1f566eb5fff4bce5920da1fa503760f.png)
                    100% no-repeat;
                  background-size: 100% 100%;
                  margin: 14rpx 0 0 14rpx;
                }
              }
            }
          }
        }
      }
      .text-wrapper_16 {
        position: absolute;
        left: 410rpx;
        top: 918rpx;
        width: 380rpx;
        height: 48rpx;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 48rpx;
        .text_31 {
          width: 380rpx;
          height: 48rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 32rpx;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 48rpx;
        }
        .text_32 {
          width: 380rpx;
          height: 48rpx;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 26rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 48rpx;
        }
      }
      .image_2 {
        position: absolute;
        left: 942rpx;
        top: 436rpx;
        width: 174rpx;
        height: 64rpx;
      }
    }
    .block_5 {
      background-color: rgba(255, 255, 255, 1);
      position: absolute;
      left: 378rpx;
      top: 1864rpx;
      width: 750rpx;
      height: 630rpx;
      .box_6 {
        background-color: rgba(245, 245, 245, 1);
        width: 750rpx;
        height: 20rpx;
      }
      .text_33 {
        width: 128rpx;
        height: 38rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 32rpx;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 32rpx;
        margin: 18rpx 0 0 54rpx;
      }
      .box_7 {
        width: 634rpx;
        height: 88rpx;
        margin: 40rpx 0 0 58rpx;
        .image-wrapper_3 {
          background-color: rgba(233, 248, 241, 1);
          border-radius: 50%;
          height: 88rpx;
          width: 88rpx;
          .label_2 {
            width: 46rpx;
            height: 46rpx;
            margin: 20rpx 0 0 22rpx;
          }
        }
        .text-group_3 {
          width: 270rpx;
          height: 72rpx;
          margin: 6rpx 0 0 14rpx;
          .text_34 {
            width: 112rpx;
            height: 34rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 28rpx;
          }
          .text-wrapper_17 {
            width: 270rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 10rpx;
            .text_35 {
              width: 270rpx;
              height: 28rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_36 {
              width: 270rpx;
              height: 28rpx;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }
        }
        .text-wrapper_18 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 60rpx;
          width: 142rpx;
          margin: 12rpx 0 0 120rpx;
          .text_37 {
            width: 78rpx;
            height: 32rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 26rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26rpx;
            margin: 14rpx 0 0 32rpx;
          }
        }
      }
      .box_8 {
        width: 634rpx;
        height: 88rpx;
        margin: 32rpx 0 0 60rpx;
        .label_3 {
          width: 88rpx;
          height: 88rpx;
        }
        .text-group_4 {
          width: 270rpx;
          height: 72rpx;
          margin: 6rpx 0 0 14rpx;
          .text_38 {
            width: 56rpx;
            height: 34rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 28rpx;
          }
          .text-wrapper_19 {
            width: 270rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 10rpx;
            .text_39 {
              width: 270rpx;
              height: 28rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_40 {
              width: 270rpx;
              height: 28rpx;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }
        }
        .text-wrapper_20 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 60rpx;
          width: 142rpx;
          margin: 12rpx 0 0 120rpx;
          .text_41 {
            width: 78rpx;
            height: 32rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 26rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26rpx;
            margin: 14rpx 0 0 32rpx;
          }
        }
      }
      .box_9 {
        width: 634rpx;
        height: 88rpx;
        margin: 38rpx 0 0 60rpx;
        .section_14 {
          background-color: rgba(255, 245, 240, 1);
          border-radius: 50%;
          height: 88rpx;
          width: 88rpx;
          .box_10 {
            background-color: rgba(255, 104, 83, 1);
            width: 52rpx;
            height: 48rpx;
            margin: 22rpx 0 0 18rpx;
          }
        }
        .text-group_5 {
          width: 270rpx;
          height: 72rpx;
          margin: 6rpx 0 0 14rpx;
          .text_42 {
            width: 56rpx;
            height: 34rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 28rpx;
          }
          .text-wrapper_21 {
            width: 270rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 10rpx;
            .text_43 {
              width: 270rpx;
              height: 28rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_44 {
              width: 270rpx;
              height: 28rpx;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }
        }
        .text-wrapper_22 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 60rpx;
          width: 142rpx;
          margin: 12rpx 0 0 120rpx;
          .text_45 {
            width: 78rpx;
            height: 32rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 26rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26rpx;
            margin: 14rpx 0 0 32rpx;
          }
        }
      }
      .box_11 {
        width: 634rpx;
        height: 88rpx;
        margin: 40rpx 0 52rpx 62rpx;
        .image-wrapper_4 {
          background-color: rgba(241, 251, 242, 1);
          border-radius: 50%;
          height: 88rpx;
          width: 88rpx;
          .label_4 {
            width: 48rpx;
            height: 48rpx;
            margin: 20rpx 0 0 20rpx;
          }
        }
        .text-group_6 {
          width: 270rpx;
          height: 72rpx;
          margin: 6rpx 0 0 14rpx;
          .text_46 {
            width: 112rpx;
            height: 34rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 28rpx;
          }
          .text-wrapper_23 {
            width: 270rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 10rpx;
            .text_47 {
              width: 270rpx;
              height: 28rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_48 {
              width: 270rpx;
              height: 28rpx;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }
        }
        .text-wrapper_24 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 60rpx;
          width: 142rpx;
          margin: 12rpx 0 0 120rpx;
          .text_49 {
            width: 78rpx;
            height: 32rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 26rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26rpx;
            margin: 14rpx 0 0 32rpx;
          }
        }
      }
    }
  }
}
