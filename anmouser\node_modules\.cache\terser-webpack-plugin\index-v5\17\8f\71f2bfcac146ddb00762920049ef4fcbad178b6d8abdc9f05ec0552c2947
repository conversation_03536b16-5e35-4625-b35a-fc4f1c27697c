
4ab0a5beac5fdc1207a4ef9e53c6cdc7d20399e3	{"key":"{\"terser\":\"4.8.1\",\"node_version\":\"v22.16.0\",\"terser-webpack-plugin\":\"1.4.6\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.m?js(\\\\?.*)?$\", \"i\"),\"chunkFilter\":() => true,\"warningsFilter\":() => true,\"extractComments\":false,\"sourceMap\":true,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":false,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"output\":{\"comments\":new RegExp(\"^\\\\**!|@preserve|@license|@cc_on\", \"i\")},\"compress\":{\"arrows\":false,\"collapse_vars\":false,\"comparisons\":false,\"computed_props\":false,\"hoist_funs\":false,\"hoist_props\":false,\"hoist_vars\":false,\"inline\":false,\"loops\":false,\"negate_iife\":false,\"properties\":false,\"reduce_funcs\":false,\"reduce_vars\":false,\"switches\":false,\"toplevel\":false,\"typeofs\":false,\"booleans\":true,\"if_return\":true,\"sequences\":true,\"unused\":true,\"conditionals\":true,\"dead_code\":true,\"evaluate\":true},\"mangle\":{\"safari10\":true}}},\"hash\":\"aaf8b06f8fdf96f45782b5dd0587f29b\"}","integrity":"sha512-J+YhENYbgGaZz6qkI5yuIgFurIe3ep9Au/9XJy1bz9FRn05h9PGFzQH6GosmuqtirBv9vINgwDRXbEk1VNSe0A==","time":1755098573505,"size":24778}