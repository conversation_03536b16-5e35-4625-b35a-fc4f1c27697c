
184b3bb49f849b110bae91d98e9e8bbbeb429443	{"key":"{\"terser\":\"4.8.1\",\"node_version\":\"v22.16.0\",\"terser-webpack-plugin\":\"1.4.6\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.m?js(\\\\?.*)?$\", \"i\"),\"chunkFilter\":() => true,\"warningsFilter\":() => true,\"extractComments\":false,\"sourceMap\":true,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":false,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"output\":{\"comments\":new RegExp(\"^\\\\**!|@preserve|@license|@cc_on\", \"i\")},\"compress\":{\"arrows\":false,\"collapse_vars\":false,\"comparisons\":false,\"computed_props\":false,\"hoist_funs\":false,\"hoist_props\":false,\"hoist_vars\":false,\"inline\":false,\"loops\":false,\"negate_iife\":false,\"properties\":false,\"reduce_funcs\":false,\"reduce_vars\":false,\"switches\":false,\"toplevel\":false,\"typeofs\":false,\"booleans\":true,\"if_return\":true,\"sequences\":true,\"unused\":true,\"conditionals\":true,\"dead_code\":true,\"evaluate\":true},\"mangle\":{\"safari10\":true}}},\"hash\":\"62d8e715933317f60b9379250cefee8f\"}","integrity":"sha512-lGhwnI2WyrUqGxfZcNcYrERh5G4sfpQHx9NxO+piv6dMtHJ6SbzxCsrQUxTr8ucAGHWhrwttCMSQ3ehh9IOLXg==","time":1755098573542,"size":33823}